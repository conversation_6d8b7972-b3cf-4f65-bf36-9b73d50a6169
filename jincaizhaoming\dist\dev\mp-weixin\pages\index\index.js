"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  setup() {
    const bannerList = common_vendor.ref([
      { id: 1, image: "/static/banner/banner1.png" },
      { id: 2, image: "/static/banner/banner2.png" },
      { id: 3, image: "/static/banner/banner3.png" }
    ]);
    common_vendor.ref(null);
    const callLoginFunction = async () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:59", "开始获取文件列表和测试云存储");
      try {
        const db = common_vendor.wx$1.cloud.database();
        const _ = db.command;
        db.collection("images").where({
          //
          category: _.eq("aa")
        }).get().then((res) => {
          common_vendor.index.__f__("log", "at pages/index/index.vue:76", res.data);
          let i = 0;
          for (let filedt of res.data) {
            bannerList.value[i].image = res.data[i].fileid;
            i = i + 1;
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:86", "登录失败", e);
      }
    };
    const currentIndex = common_vendor.ref(0);
    const designCategories = common_vendor.ref([
      { id: 1, name: "家居吸顶" },
      { id: 2, name: "家居吊灯" },
      { id: 3, name: "磁吸线性灯" },
      { id: 4, name: "筒灯射灯灯带" },
      { id: 5, name: "商照" },
      { id: 6, name: "开关配件" }
    ]);
    const recommendList = common_vendor.ref([
      { id: 1, image: "/static/recommend/r1.png" },
      { id: 2, image: "/static/recommend/r2.png" },
      { id: 3, image: "/static/recommend/r3.png" },
      { id: 4, image: "/static/recommend/r4.png" }
    ]);
    const goToDesign = (item) => {
      common_vendor.index.navigateTo({
        url: "/pages/design/detail?id=" + item.id + "&name=" + item.name
      });
    };
    const viewRecommend = (item) => {
      common_vendor.index.previewImage({
        urls: recommendList.value.map((item2) => item2.image),
        current: item.image
      });
    };
    return {
      currentIndex,
      bannerList,
      designCategories,
      recommendList,
      goToDesign,
      viewRecommend,
      callLoginFunction
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.callLoginFunction && $setup.callLoginFunction(...args)),
    b: _ctx.userInfo
  }, _ctx.userInfo ? {
    c: common_vendor.t(_ctx.userInfo.openid)
  } : {}, {
    d: common_vendor.f($setup.bannerList, (item, index, i0) => {
      return {
        a: item.image,
        b: index
      };
    }),
    e: common_vendor.t($setup.currentIndex + 1),
    f: common_vendor.t($setup.bannerList.length),
    g: common_vendor.f($setup.designCategories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.o(($event) => $setup.goToDesign(item), index)
      };
    }),
    h: common_vendor.f($setup.recommendList, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(index + 1),
        c: index,
        d: common_vendor.o(($event) => $setup.viewRecommend(item), index)
      };
    }),
    i: common_vendor.t($setup.recommendList.length)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
