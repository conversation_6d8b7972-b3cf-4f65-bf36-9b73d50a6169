"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  setup() {
    const categoryMapping = {
      "aa": "主页，顶部轮动（3张图）",
      "ab": "主页，伴侣轮动（4张图）",
      "ac": "主页，实用吸顶",
      "ad": "主页，家居吊灯",
      "ae": "主页，磁吸线性灯",
      "af": "主页，筒灯射灯灯带",
      "ag": "主页，商照",
      "ah": "主页，开关配件",
      "ai": "主页，新品开发订制（1张图）",
      "aj": "主页，阳光灿烂（1张图）",
      "ba": "分类，客厅灯",
      "bb": "分类，卧室灯",
      "bc": "分类，吊灯",
      "bd": "分类，磁吸灯",
      "be": "分类，线性灯",
      "bf": "分类，风扇灯",
      "bg": "分类，餐吊灯",
      "bh": "分类，筒灯",
      "bi": "分类，射灯",
      "bj": "分类，灯带",
      "bk": "分类，过道灯",
      "bl": "分类，壁灯",
      "bm": "分类，浴霸",
      "bn": "分类，集成灯",
      "bo": "分类，T5T8灯管",
      "bp": "分类，办公灯",
      "bq": "分类，节日灯串",
      "br": "分类，光源灯配",
      "bs": "分类，开关",
      "bt": "分类，商照"
    };
    const bannerList = common_vendor.ref([]);
    const userInfo = common_vendor.ref(null);
    const loading = common_vendor.ref(false);
    const getImagesFromCloud = async (category, targetArray, maxCount = null) => {
      try {
        const db = common_vendor.wx$1.cloud.database();
        const _ = db.command;
        const res = await db.collection("images").where({
          category: _.eq(category)
        }).get();
        common_vendor.index.__f__("log", "at pages/index/index.vue:114", `获取${category}分类图片:`, res.data);
        if (res.data && res.data.length > 0) {
          const dataToUse = maxCount ? res.data.slice(0, maxCount) : res.data;
          dataToUse.forEach((item, index) => {
            if (targetArray.value[index]) {
              targetArray.value[index].image = item.fileid;
              targetArray.value[index].filename = item.filename || "";
            } else {
              targetArray.value.push({
                id: index + 1,
                image: item.fileid,
                filename: item.filename || ""
              });
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:137", `获取${category}分类图片失败:`, error);
      }
    };
    const loadBannerImages = async () => {
      await getImagesFromCloud("aa", bannerList, 3);
    };
    const callLoginFunction = async () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:147", "开始获取文件列表和测试云存储");
      await loadBannerImages();
    };
    const currentIndex = common_vendor.ref(0);
    const designCategories = common_vendor.ref([
      { id: 1, name: "家居吸顶" },
      { id: 2, name: "家居吊灯" },
      { id: 3, name: "磁吸线性灯" },
      { id: 4, name: "筒灯射灯灯带" },
      { id: 5, name: "商照" },
      { id: 6, name: "开关配件" },
      { id: 7, name: "新品开发定制" }
    ]);
    const recommendList = common_vendor.ref([]);
    const loadRecommendImages = async () => {
      await getImagesFromCloud("ab", recommendList, 4);
    };
    const initPageData = async () => {
      loading.value = true;
      try {
        await Promise.all([
          loadBannerImages(),
          loadRecommendImages()
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:178", "初始化页面数据失败:", error);
      } finally {
        loading.value = false;
      }
    };
    common_vendor.onMounted(() => {
      initPageData();
    });
    const goToDesign = (item) => {
      common_vendor.index.navigateTo({
        url: "/pages/design/detail?id=" + item.id + "&name=" + encodeURIComponent(item.name)
      });
    };
    const viewRecommend = (item) => {
      common_vendor.index.previewImage({
        urls: recommendList.value.map((item2) => item2.image),
        current: item.image
      });
    };
    return {
      currentIndex,
      bannerList,
      designCategories,
      recommendList,
      loading,
      userInfo,
      categoryMapping,
      goToDesign,
      viewRecommend,
      callLoginFunction,
      initPageData
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $setup.userInfo
  }, $setup.userInfo ? {
    b: common_vendor.t($setup.userInfo.openid)
  } : {}, {
    c: $setup.loading
  }, $setup.loading ? {} : {}, {
    d: $setup.bannerList.length > 0
  }, $setup.bannerList.length > 0 ? {
    e: common_vendor.f($setup.bannerList, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: item.filename
      }, item.filename ? {
        c: common_vendor.t(item.filename)
      } : {}, {
        d: common_vendor.t(index + 1),
        e: index
      });
    }),
    f: common_vendor.t($setup.bannerList.length)
  } : !$setup.loading ? {} : {}, {
    g: !$setup.loading,
    h: common_vendor.f($setup.designCategories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.o(($event) => $setup.goToDesign(item), index)
      };
    }),
    i: $setup.recommendList.length > 0
  }, $setup.recommendList.length > 0 ? {
    j: common_vendor.f($setup.recommendList, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: item.filename
      }, item.filename ? {
        c: common_vendor.t(item.filename)
      } : {}, {
        d: common_vendor.t(index + 1),
        e: index,
        f: common_vendor.o(($event) => $setup.viewRecommend(item), index)
      });
    }),
    k: common_vendor.t($setup.recommendList.length)
  } : !$setup.loading ? {} : {}, {
    l: !$setup.loading
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
