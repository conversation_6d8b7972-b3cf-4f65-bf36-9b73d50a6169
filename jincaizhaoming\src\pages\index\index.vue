<template>
	<view class="content">
		<view class="safe-area-top"></view>
		<!-- 页面标题 -->
		<view class="page-title">简单参考/成为专家</view>
		  <button @click="callLoginFunction">微信一键登录</button>
		  <view v-if="userInfo">
		    您的ID: {{ userInfo.openid }}
		  </view>
		<!-- 轮播图功能 -->
		<swiper class="banner-swiper" circular indicator-dots autoplay interval="3000" duration="500">
			<swiper-item v-for="(item, index) in bannerList" :key="index">
				<view class="swiper-item">
					<image :src="item.image" mode="aspectFill"></image>
					<view class="swiper-count">{{ currentIndex + 1 }}/{{ bannerList.length }}张图</view>
				</view>
			</swiper-item>
		</swiper>
		
		<!-- 组合设计参考 -->
		<view class="section">
			<view class="section-title">组合设计参考</view>
			<view class="category-grid">
				<view class="category-item" v-for="(item, index) in designCategories" :key="index" @click="goToDesign(item)">
					{{ item.name }}
				</view>
			</view>
		</view>
		
		<!-- 推荐设计参考 -->
		<view class="section">
			<view class="section-title">推荐设计参考</view>
			<swiper class="recommend-swiper" circular indicator-dots autoplay interval="3000" duration="500">
				<swiper-item v-for="(item, index) in recommendList" :key="index" @click="viewRecommend(item)">
					<view class="recommend-item">
						<image :src="item.image" mode="aspectFill"></image>
						<view class="recommend-count">{{ index + 1 }}/{{ recommendList.length }}张图</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
import { ref } from 'vue'

export default {
	setup() {
		
		const bannerList = ref([
			{ id: 1, image: '/static/banner/banner1.png' },
			{ id: 2, image: '/static/banner/banner2.png' },
			{ id: 3, image: '/static/banner/banner3.png' }
		])
		const userInfo = ref(null)
		
		const callLoginFunction = async () => {
			console.log('开始获取文件列表和测试云存储')
		  try {
			  // #ifdef MP-WEIXIN
		
			  
		    const res = await wx.cloud.callFunction({
		      name: 'getFiles', // 对应云函数目录名
		      data: {
		        action: 'getTempFileURL',
		        directory: 'jiandancankao/'
		      } // 传递参数
		    })

		    if (res.result && res.result.code === 0) {
		      console.log('获取文件下载链接成功:', res.result.data)
		      console.log('文件数量:', res.result.data.fileCount)
		      console.log('文件列表:', res.result.data.files)

		      // 如果有文件，可以使用第一个文件的临时URL更新轮播图
		      if (res.result.data.files && res.result.data.files.length > 0) {
		        const firstFile = res.result.data.files[0]
		        if (firstFile.tempFileURL && firstFile.status === 0) {
		          bannerList.value[0].image = firstFile.tempFileURL
		          console.log('已更新轮播图:', firstFile.tempFileURL)
		        }
		      }
		    } else if (res.result && res.result.code === 200) {
		      console.log('提示信息:', res.result.message)
		      console.log('建议:', res.result.data.suggestion)
		    } else {
		      console.error('获取文件失败:', res.result)
		    }
			
			
			// 需先使用 wx.cloud.init 初始化，小程序端无需再引入 SDK，且免鉴权
			wx.cloud
			  .downloadFile({
			    fileID: "cloud://cloud1-8gakkk4ucebe9f24.636c-cloud1-8gakkk4ucebe9f24-1364517373/ketingdeng/2.png" // 文件 ID
			  })
			  .then((res) => {
			    // 返回临时文件路径
			    console.log(res.tempFilePath);
				bannerList.value[0].image = res.tempFilePath
			  });
			  
			  // 1. 获取数据库引用
			  const db = wx.cloud.database();
			  ﻿
			  const _ = db.command;
			  db.collection("images")
			    .where({
			      // gt 方法用于指定一个 "大于" 条件，此处 _.gt(30) 是一个 "大于 30" 的条件
			      category: _.eq('jc1')
			    })
			    .get()
			    .then((res) => {
			      console.log(res.data);
				  
				  bannerList.value[1].image = res.data.fileid;
			    });
			
			// #endif
		  } catch (e) {
		    console.error('登录失败', e)
		  }
		}
		const currentIndex = ref(0)

		const designCategories = ref([
			{ id: 1, name: '家居吸顶' },
			{ id: 2, name: '家居吊灯' },
			{ id: 3, name: '磁吸线性灯' },
			{ id: 4, name: '筒灯射灯灯带' },
			{ id: 5, name: '商照' },
			{ id: 6, name: '开关配件' }
		])
		const recommendList = ref([
			{ id: 1, image: '/static/recommend/r1.png' },
			{ id: 2, image: '/static/recommend/r2.png' },
			{ id: 3, image: '/static/recommend/r3.png' },
			{ id: 4, image: '/static/recommend/r4.png' }
		])
		
		const goToDesign = (item) => {
			uni.navigateTo({
				url: '/pages/design/detail?id=' + item.id + '&name=' + item.name
			})
		}
		
		const viewRecommend = (item) => {
			uni.previewImage({
				urls: recommendList.value.map(item => item.image),
				current: item.image
			})
		}
		
		return {
			currentIndex,
			bannerList,
			designCategories,
			recommendList,
			goToDesign,
			viewRecommend,
			callLoginFunction
		}
	}
}
</script>

<style lang="scss">
.safe-area-top {
	height: var(--status-bar-height, 44px);
	width: 100%;
}
.content {
	padding-bottom: 30rpx;
}

.page-title {
	padding: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	background-color: #fff;
	border-bottom: 1px solid #eee;
}

.banner-swiper {
	width: 100%;
	height: 500rpx;
	
	.swiper-item {
		position: relative;
		width: 100%;
		height: 100%;
		
		image {
			width: 100%;
			height: 100%;
		}
		
		.swiper-count {
			position: absolute;
			bottom: 20rpx;
			left: 20rpx;
			background-color: rgba(0, 0, 0, 0.5);
			color: #fff;
			padding: 5rpx 15rpx;
			border-radius: 20rpx;
			font-size: 24rpx;
		}
	}
}

.section {
	margin-top: 30rpx;
	padding: 0 20rpx;
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		padding-left: 20rpx;
		border-left: 8rpx solid #3cc51f;
	}
}

.category-grid {
	display: flex;
	flex-wrap: wrap;
	
	.category-item {
		width: 33.33%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border: 1rpx solid #eee;
		font-size: 28rpx;
		box-sizing: border-box;
	}
}

.recommend-swiper {
	width: 100%;
	height: 400rpx;
	
	.recommend-item {
		position: relative;
		width: 100%;
		height: 100%;
		
		image {
			width: 100%;
			height: 100%;
			border-radius: 10rpx;
		}
		
		.recommend-count {
			position: absolute;
			bottom: 20rpx;
			left: 20rpx;
			background-color: rgba(0, 0, 0, 0.5);
			color: #fff;
			padding: 5rpx 15rpx;
			border-radius: 20rpx;
			font-size: 24rpx;
		}
	}
}
</style>
