<template>
	<view class="content">
		<view class="safe-area-top"></view>
		<!-- 页面标题 -->
		<view class="page-title">简单参考/成为专家</view>
		  <view v-if="userInfo">
		    您的ID: {{ userInfo.openid }}
		  </view>
		  <!-- 加载状态 -->
		  <view v-if="loading" class="loading-container">
		    <text class="loading-text">正在加载图片数据...</text>
		  </view>
		<!-- 轮播图功能 -->
		<swiper class="banner-swiper" circular indicator-dots autoplay interval="3000" duration="500" v-if="bannerList.length > 0">
			<swiper-item v-for="(item, index) in bannerList" :key="index">
				<view class="swiper-item">
					<image :src="item.image" mode="aspectFill"></image>
					<view class="swiper-filename" v-if="item.filename">{{ item.filename }}</view>
					<view class="swiper-count">{{ index + 1 }}/{{ bannerList.length }}张图</view>
				</view>
			</swiper-item>
		</swiper>
		<!-- 轮播图空状态 -->
		<view class="banner-empty" v-else-if="!loading">
			<text class="empty-text">暂无轮播图数据</text>
		</view>
		
		<!-- 组合设计参考 -->
		<view class="section">
			<view class="section-title">组合设计参考</view>
			<view class="category-grid">
				<view class="category-item" v-for="(item, index) in designCategories" :key="index" @click="goToDesign(item)">
					{{ item.name }}
				</view>
			</view>
		</view>
		
		<!-- 推荐设计参考 -->
		<view class="section">
			<view class="section-title">推荐设计参考</view>
			<swiper class="recommend-swiper" circular indicator-dots autoplay interval="3000" duration="500" v-if="recommendList.length > 0">
				<swiper-item v-for="(item, index) in recommendList" :key="index" @click="viewRecommend(item)">
					<view class="recommend-item">
						<image :src="item.image" mode="aspectFill"></image>
						<view class="recommend-filename" v-if="item.filename">{{ item.filename }}</view>
						<view class="recommend-count">{{ index + 1 }}/{{ recommendList.length }}张图</view>
					</view>
				</swiper-item>
			</swiper>
			<!-- 推荐设计参考空状态 -->
			<view class="recommend-empty" v-else-if="!loading">
				<text class="empty-text">暂无推荐设计数据</text>
			</view>
		</view>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
	setup() {
		// 分类映射关系
		const categoryMapping = {
			'aa': '主页，顶部轮动（3张图）',
			'ab': '主页，伴侣轮动（4张图）',
			'ac': '主页，实用吸顶',
			'ad': '主页，家居吊灯',
			'ae': '主页，磁吸线性灯',
			'af': '主页，筒灯射灯灯带',
			'ag': '主页，商照',
			'ah': '主页，开关配件',
			'ai': '主页，新品开发订制（1张图）',
			'aj': '主页，阳光灿烂（1张图）',
			'ba': '分类，客厅灯',
			'bb': '分类，卧室灯',
			'bc': '分类，吊灯',
			'bd': '分类，磁吸灯',
			'be': '分类，线性灯',
			'bf': '分类，风扇灯',
			'bg': '分类，餐吊灯',
			'bh': '分类，筒灯',
			'bi': '分类，射灯',
			'bj': '分类，灯带',
			'bk': '分类，过道灯',
			'bl': '分类，壁灯',
			'bm': '分类，浴霸',
			'bn': '分类，集成灯',
			'bo': '分类，T5T8灯管',
			'bp': '分类，办公灯',
			'bq': '分类，节日灯串',
			'br': '分类，光源灯配',
			'bs': '分类，开关',
			'bt': '分类，商照'
		}

		const bannerList = ref([])
		const userInfo = ref(null)
		const loading = ref(false)

		// 从云数据库获取图片数据
		const getImagesFromCloud = async (category, targetArray, maxCount = null) => {
			try {
				// #ifdef MP-WEIXIN
				const db = wx.cloud.database();
				const _ = db.command;

				const res = await db.collection("images")
					.where({
						category: _.eq(category)
					})
					.get();

				console.log(`获取${category}分类图片:`, res.data);

				if (res.data && res.data.length > 0) {
					// 如果指定了最大数量，则限制数量
					const dataToUse = maxCount ? res.data.slice(0, maxCount) : res.data;

					// 更新目标数组
					dataToUse.forEach((item, index) => {
						if (targetArray.value[index]) {
							targetArray.value[index].image = item.fileid;
							targetArray.value[index].filename = item.filename || '';
						} else {
							// 如果目标数组长度不够，添加新项
							targetArray.value.push({
								id: index + 1,
								image: item.fileid,
								filename: item.filename || ''
							});
						}
					});
				}
				// #endif
			} catch (error) {
				console.error(`获取${category}分类图片失败:`, error);
			}
		}

		// 加载轮播图数据
		const loadBannerImages = async () => {
			await getImagesFromCloud('aa', bannerList, 3);
		}

		const callLoginFunction = async () => {
			console.log('开始获取文件列表和测试云存储')
			await loadBannerImages();
		}

		const currentIndex = ref(0)

		const designCategories = ref([
			{ id: 1, name: '家居吸顶' },
			{ id: 2, name: '家居吊灯' },
			{ id: 3, name: '磁吸线性灯' },
			{ id: 4, name: '筒灯射灯灯带' },
			{ id: 5, name: '商照' },
			{ id: 6, name: '开关配件' },
			{ id: 7, name: '新品开发定制' }
		])
		const recommendList = ref([])

		// 加载推荐图片数据
		const loadRecommendImages = async () => {
			await getImagesFromCloud('ab', recommendList, 4);
		}

		// 页面初始化时加载数据
		const initPageData = async () => {
			loading.value = true;
			try {
				await Promise.all([
					loadBannerImages(),
					loadRecommendImages()
				]);
			} catch (error) {
				console.error('初始化页面数据失败:', error);
			} finally {
				loading.value = false;
			}
		}

		// 页面挂载时自动加载数据
		onMounted(() => {
			initPageData();
		});

		const goToDesign = (item) => {
			uni.navigateTo({
				url: '/pages/design/detail?id=' + item.id + '&name=' + encodeURIComponent(item.name)
			})
		}

		const viewRecommend = (item) => {
			uni.previewImage({
				urls: recommendList.value.map(item => item.image),
				current: item.image
			})
		}

		return {
			currentIndex,
			bannerList,
			designCategories,
			recommendList,
			loading,
			userInfo,
			categoryMapping,
			goToDesign,
			viewRecommend,
			callLoginFunction,
			initPageData
		}
	}
}
</script>

<style lang="scss">
.safe-area-top {
	height: var(--status-bar-height, 44px);
	width: 100%;
}
.content {
	padding-bottom: 30rpx;
}

.page-title {
	padding: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	background-color: #fff;
	border-bottom: 1px solid #eee;
}

.loading-container {
	padding: 20rpx;
	text-align: center;
	background-color: #fff;

	.loading-text {
		color: #666;
		font-size: 28rpx;
	}
}

.banner-swiper {
	width: 100%;
	height: 500rpx;

	.swiper-item {
		position: relative;
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100%;
		}

		.swiper-filename {
			position: absolute;
			top: 20rpx;
			left: 20rpx;
			background-color: rgba(0, 0, 0, 0.7);
			color: #fff;
			padding: 5rpx 15rpx;
			border-radius: 20rpx;
			font-size: 22rpx;
			max-width: 60%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.swiper-count {
			position: absolute;
			bottom: 20rpx;
			left: 20rpx;
			background-color: rgba(0, 0, 0, 0.5);
			color: #fff;
			padding: 5rpx 15rpx;
			border-radius: 20rpx;
			font-size: 24rpx;
		}
	}
}

.banner-empty {
	width: 100%;
	height: 500rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;

	.empty-text {
		color: #999;
		font-size: 28rpx;
	}
}

.section {
	margin-top: 30rpx;
	padding: 0 20rpx;
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		padding-left: 20rpx;
		border-left: 8rpx solid #3cc51f;
	}
}

.category-grid {
	display: flex;
	flex-wrap: wrap;
	
	.category-item {
		width: 33.33%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border: 1rpx solid #eee;
		font-size: 28rpx;
		box-sizing: border-box;
	}
}

.recommend-swiper {
	width: 100%;
	height: 400rpx;

	.recommend-item {
		position: relative;
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100%;
			border-radius: 10rpx;
		}

		.recommend-filename {
			position: absolute;
			top: 20rpx;
			left: 20rpx;
			background-color: rgba(0, 0, 0, 0.7);
			color: #fff;
			padding: 5rpx 15rpx;
			border-radius: 20rpx;
			font-size: 22rpx;
			max-width: 60%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.recommend-count {
			position: absolute;
			bottom: 20rpx;
			left: 20rpx;
			background-color: rgba(0, 0, 0, 0.5);
			color: #fff;
			padding: 5rpx 15rpx;
			border-radius: 20rpx;
			font-size: 24rpx;
		}
	}
}

.recommend-empty {
	width: 100%;
	height: 400rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	border-radius: 10rpx;
	margin-top: 20rpx;

	.empty-text {
		color: #999;
		font-size: 28rpx;
	}
}
</style>
