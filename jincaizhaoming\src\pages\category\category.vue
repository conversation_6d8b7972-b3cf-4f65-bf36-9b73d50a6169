<template>
	<view class="category-root">
		<view class="safe-area-top"></view>
		<view class="category-main">
			<!-- 左侧分类列表 -->
			<scroll-view class="category-list" scroll-y>
				<view
					v-for="(item, index) in categories"
					:key="index"
					:class="['category-item', currentIndex === index ? 'active' : '']"
					@click="selectCategory(index)"
				>
					{{ item }}
				</view>
			</scroll-view>

			<!-- 右侧商品网格 -->
			<scroll-view class="goods-list" scroll-y>
				<view v-if="loading" class="loading-container">
					<text class="loading-text">正在加载图片...</text>
				</view>
				<view class="goods-grid" v-else-if="currentGoods.length > 0">
					<view class="goods-item" v-for="(good, idx) in currentGoods" :key="idx" @click="previewImage(good)">
						<image class="goods-img" :src="good.image" mode="aspectFill" />
						<view class="goods-title">{{ good.filename || `图片${idx + 1}` }}</view>
					</view>
				</view>
				<view class="empty-container" v-else>
					<text class="empty-text">暂无图片数据</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue'

// 分类名称到分类代码的映射
const categoryMapping = {
	'客厅灯': 'ba',
	'卧室灯': 'bb',
	'吊灯': 'bc',
	'磁吸灯': 'bd',
	'线性灯': 'be',
	'风扇灯': 'bf',
	'餐吊灯': 'bg',
	'筒灯': 'bh',
	'射灯': 'bi',
	'灯带': 'bj',
	'过道灯': 'bk',
	'壁灯': 'bl',
	'浴霸': 'bm',
	'集成灯': 'bn',
	'T5T8灯管': 'bo',
	'办公灯': 'bp',
	'节日灯串': 'bq',
	'光源灯配': 'br',
	'开关': 'bs',
	'商照': 'bt'
}

const categoryNames = [
	'客厅灯', '卧室灯', '吊灯', '磁吸灯', '线性灯', '风扇灯', '餐吊灯', '筒灯', '射灯', '灯带',
	'过道灯', '壁灯', '浴霸', '集成灯', 'T5T8灯管', '办公灯', '节日灯串', '光源灯配', '开关', '商照'
]

export default {
	setup() {
		const categories = ref(categoryNames)
		const currentIndex = ref(0)
		const currentGoods = ref([])
		const loading = ref(false)

		// 从云数据库获取图片数据
		const getImagesFromCloud = async (categoryCode) => {
			try {
				loading.value = true
				// #ifdef MP-WEIXIN
				const db = wx.cloud.database();
				const _ = db.command;

				const res = await db.collection("images")
					.where({
						category: _.eq(categoryCode)
					})
					.get();

				console.log(`获取${categoryCode}分类图片:`, res.data);

				if (res.data && res.data.length > 0) {
					currentGoods.value = res.data.map((item, index) => ({
						id: index + 1,
						image: item.fileid,
						filename: item.filename || ''
					}));
				} else {
					currentGoods.value = [];
				}
				// #endif
			} catch (error) {
				console.error(`获取${categoryCode}分类图片失败:`, error);
				currentGoods.value = [];
			} finally {
				loading.value = false
			}
		}

		// 选择分类
		const selectCategory = async (idx) => {
			currentIndex.value = idx
			const categoryName = categories.value[idx]
			const categoryCode = categoryMapping[categoryName]

			if (categoryCode) {
				await getImagesFromCloud(categoryCode)
			} else {
				console.warn(`未找到分类 ${categoryName} 对应的代码`)
				currentGoods.value = []
			}
		}

		// 预览图片
		const previewImage = (item) => {
			const urls = currentGoods.value.map(img => img.image)
			uni.previewImage({
				urls: urls,
				current: item.image
			})
		}

		// 页面挂载时加载第一个分类的数据
		onMounted(() => {
			if (categories.value.length > 0) {
				selectCategory(0)
			}
		})

		return {
			categories,
			currentIndex,
			currentGoods,
			loading,
			selectCategory,
			previewImage
		}
	}
}
</script>

<style lang="scss">
.safe-area-top {
	height: var(--status-bar-height, 100px);
	width: 100%;
}
.category-root {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #f8f8f8;
}
.category-main {
	flex: 1;
	display: flex;
	height: calc(100vh - var(--status-bar-height, 44px));
}
.category-list {
	width: 180rpx;
	background: #f4f4f4;
	height: 100%;
	overflow: auto;
	.category-item {
		padding: 24rpx 0;
		text-align: center;
		font-size: 28rpx;
		color: #333;
		cursor: pointer;
		&.active {
			color: #3cc51f;
			font-weight: bold;
			background: #fff;
		}
	}
}
.goods-list {
	flex: 1;
	height: 100%;
	background: #fff;
	overflow: auto;
	padding: 0 10rpx;

	.loading-container {
		padding: 40rpx;
		text-align: center;

		.loading-text {
			color: #666;
			font-size: 28rpx;
		}
	}

	.empty-container {
		padding: 40rpx;
		text-align: center;

		.empty-text {
			color: #999;
			font-size: 28rpx;
		}
	}

	.goods-grid {
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;
		padding-top: 20rpx;
		box-sizing: border-box;
		.goods-item {
			width: 47%;
			margin: 1.5%;
			background: #fff;
			border-radius: 12rpx;
			box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
			padding-bottom: 20rpx;
			cursor: pointer;

			&:active {
				opacity: 0.8;
			}

			.goods-img {
				width: 100%;
				height: 180rpx;
				border-radius: 12rpx 12rpx 0 0;
				background: #eee;
			}
			.goods-title {
				padding: 16rpx 10rpx 0 10rpx;
				font-size: 26rpx;
				color: #222;
				text-align: center;
				word-break: break-all;
				line-height: 1.4;
			}
		}
	}
}
</style> 