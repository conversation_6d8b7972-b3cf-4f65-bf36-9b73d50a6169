/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.safe-area-top {
  height: var(--status-bar-height, 44px);
  width: 100%;
}
.content {
  padding-bottom: 30rpx;
}
.page-title {
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.loading-container {
  padding: 20rpx;
  text-align: center;
  background-color: #fff;
}
.loading-container .loading-text {
  color: #666;
  font-size: 28rpx;
}
.banner-swiper {
  width: 100%;
  height: 500rpx;
}
.banner-swiper .swiper-item {
  position: relative;
  width: 100%;
  height: 100%;
}
.banner-swiper .swiper-item image {
  width: 100%;
  height: 100%;
}
.banner-swiper .swiper-item .swiper-filename {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.banner-swiper .swiper-item .swiper-count {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.section {
  margin-top: 30rpx;
  padding: 0 20rpx;
}
.section .section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #3cc51f;
}
.category-grid {
  display: flex;
  flex-wrap: wrap;
}
.category-grid .category-item {
  width: 33.33%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border: 1rpx solid #eee;
  font-size: 28rpx;
  box-sizing: border-box;
}
.recommend-swiper {
  width: 100%;
  height: 400rpx;
}
.recommend-swiper .recommend-item {
  position: relative;
  width: 100%;
  height: 100%;
}
.recommend-swiper .recommend-item image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.recommend-swiper .recommend-item .recommend-filename {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.recommend-swiper .recommend-item .recommend-count {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}