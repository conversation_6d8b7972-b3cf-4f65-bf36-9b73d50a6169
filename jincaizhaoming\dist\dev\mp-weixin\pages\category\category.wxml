<view class="category-root"><view class="safe-area-top"></view><view class="category-main"><scroll-view class="category-list" scroll-y><view wx:for="{{a}}" wx:for-item="item" wx:key="b" class="{{['category-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></scroll-view><scroll-view class="goods-list" scroll-y><view wx:if="{{b}}" class="loading-container"><text class="loading-text">正在加载图片...</text></view><view wx:elif="{{c}}" class="goods-grid"><view wx:for="{{d}}" wx:for-item="good" wx:key="c" class="goods-item" bindtap="{{good.d}}"><image class="goods-img" src="{{good.a}}" mode="aspectFill"/><view class="goods-title">{{good.b}}</view></view></view><view wx:else class="empty-container"><text class="empty-text">暂无图片数据</text></view></scroll-view></view></view>