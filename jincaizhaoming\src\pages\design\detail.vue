<template>
	<view class="design-root">
		<view class="safe-area-top"></view>
		<view class="design-main">
			<!-- 左侧分类列表 -->
			<scroll-view class="category-list" scroll-y>
				<view
					v-for="(item, index) in designCategories"
					:key="index"
					:class="['category-item', currentIndex === index ? 'active' : '']"
					@click="selectCategory(index)"
				>
					{{ item.name }}
				</view>
			</scroll-view>

			<!-- 右侧图片网格 -->
			<scroll-view class="goods-list" scroll-y>
				<view v-if="loading" class="loading-container">
					<text class="loading-text">正在加载图片...</text>
				</view>
				<view class="goods-grid" v-else>
					<view class="goods-item" v-for="(item, idx) in currentImages" :key="idx" @click="previewImage(item, idx)">
						<image class="goods-img" :src="item.image" mode="aspectFill" />
						<view class="goods-title">{{ item.filename || `图片${idx + 1}` }}</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
	setup() {
		// 组合设计参考分类映射
		const designCategoryMapping = {
			'ac': { name: '家居吸顶', category: 'ac' },
			'ad': { name: '家居吊灯', category: 'ad' },
			'ae': { name: '磁吸线性灯', category: 'ae' },
			'af': { name: '筒灯射灯灯带', category: 'af' },
			'ag': { name: '商照', category: 'ag' },
			'ah': { name: '开关配件', category: 'ah' }
		}

		const designCategories = ref([
			{ id: 1, name: '家居吸顶', category: 'ac' },
			{ id: 2, name: '家居吊灯', category: 'ad' },
			{ id: 3, name: '磁吸线性灯', category: 'ae' },
			{ id: 4, name: '筒灯射灯灯带', category: 'af' },
			{ id: 5, name: '商照', category: 'ag' },
			{ id: 6, name: '开关配件', category: 'ah' }
		])

		const currentIndex = ref(0)
		const currentImages = ref([])
		const loading = ref(false)

		// 获取页面参数
		const getPageParams = () => {
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]
			return currentPage.options || {}
		}

		// 从云数据库获取图片数据
		const getImagesFromCloud = async (category) => {
			try {
				loading.value = true
				// #ifdef MP-WEIXIN
				const db = wx.cloud.database();
				const _ = db.command;

				const res = await db.collection("images")
					.where({
						category: _.eq(category)
					})
					.get();

				console.log(`获取${category}分类图片:`, res.data);

				if (res.data && res.data.length > 0) {
					currentImages.value = res.data.map((item, index) => ({
						id: index + 1,
						image: item.fileid,
						filename: item.filename || `图片${index + 1}`
					}));
				} else {
					currentImages.value = [];
				}
				// #endif
			} catch (error) {
				console.error(`获取${category}分类图片失败:`, error);
				currentImages.value = [];
			} finally {
				loading.value = false
			}
		}

		// 选择分类
		const selectCategory = async (idx) => {
			currentIndex.value = idx
			const category = designCategories.value[idx].category
			await getImagesFromCloud(category)
		}

		// 预览图片
		const previewImage = (item, index) => {
			const urls = currentImages.value.map(img => img.image)
			uni.previewImage({
				urls: urls,
				current: item.image
			})
		}

		// 页面挂载时加载数据
		onMounted(() => {
			const params = getPageParams()
			let targetIndex = 0

			// 如果有传入参数，根据参数选择对应的分类
			if (params.id) {
				const id = parseInt(params.id)
				const foundIndex = designCategories.value.findIndex(item => item.id === id)
				if (foundIndex !== -1) {
					targetIndex = foundIndex
				}
			}

			if (designCategories.value.length > 0) {
				selectCategory(targetIndex)
			}
		})

		return {
			designCategories,
			currentIndex,
			currentImages,
			loading,
			selectCategory,
			previewImage
		}
	}
}
</script>

<style lang="scss">
.safe-area-top {
	height: var(--status-bar-height, 44px);
	width: 100%;
}
.design-root {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #f8f8f8;
}
.design-main {
	flex: 1;
	display: flex;
	height: calc(100vh - var(--status-bar-height, 44px));
}
.category-list {
	width: 180rpx;
	background: #f4f4f4;
	height: 100%;
	overflow: auto;
	.category-item {
		padding: 24rpx 0;
		text-align: center;
		font-size: 28rpx;
		color: #333;
		cursor: pointer;
		&.active {
			color: #3cc51f;
			font-weight: bold;
			background: #fff;
		}
	}
}
.goods-list {
	flex: 1;
	height: 100%;
	background: #fff;
	overflow: auto;
	padding: 0 10rpx;
	
	.loading-container {
		padding: 40rpx;
		text-align: center;
		
		.loading-text {
			color: #666;
			font-size: 28rpx;
		}
	}
	
	.goods-grid {
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;
		padding-top: 20rpx;
		box-sizing: border-box;
		.goods-item {
			width: 47%;
			margin: 1.5%;
			background: #fff;
			border-radius: 12rpx;
			box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
			padding-bottom: 20rpx;
			cursor: pointer;
			
			&:active {
				opacity: 0.8;
			}
			
			.goods-img {
				width: 100%;
				height: 180rpx;
				border-radius: 12rpx 12rpx 0 0;
				background: #eee;
			}
			.goods-title {
				padding: 16rpx 10rpx 0 10rpx;
				font-size: 26rpx;
				color: #222;
				text-align: center;
				word-break: break-all;
				line-height: 1.4;
			}
		}
	}
}
</style>
