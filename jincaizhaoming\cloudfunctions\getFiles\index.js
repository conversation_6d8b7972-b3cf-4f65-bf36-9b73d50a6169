
const cloudbase = require('@cloudbase/node-sdk')
const CloudBase2 = require('@cloudbase/manager-node')
const app = new CloudBase2({
  envId: "cloud1-8gakkk4ucebe9f24"
});
// 获取各功能模块
const { database, functions, storage, env, commonService } = app
exports.main = async (event, context) => {
  try {
	console.log("我操了");
    const { directory } = event
	const res1 = await storage.listDirectoryFiles("jiandancankao/");
	console.log(res1);
	for (let item in res1) {
        console.log("item");
	  console.log(item);
	}
    
    // 获取云存储实例
    /*const storage = app.cloud.Storage()
    
    // 列出目录文件（默认最多1000条）
    const { fileList } = await storage.listDirectory({
      prefix: directory.endsWith('/') ? directory : `${directory}/`,
      delimiter: '/'
    })

    // 生成下载链接
    const files = await Promise.all(
      fileList.map(async file => ({
        name: file.Key,
        url: await storage.getDownloadUrl({ fileID: file.Key }),
        size: file.Size,
        lastModified: file.LastModified
      }))
    )
*/
    return {
      code: 0,
      data: {
        directory
      }
    }
  } catch (error) {
    return {
      code: 500,
      message: error.message
    }
  }
}
  