"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  setup() {
    const designCategories = common_vendor.ref([
      { id: 1, name: "家居吸顶", category: "ac" },
      { id: 2, name: "家居吊灯", category: "ad" },
      { id: 3, name: "磁吸线性灯", category: "ae" },
      { id: 4, name: "筒灯射灯灯带", category: "af" },
      { id: 5, name: "商照", category: "ag" },
      { id: 6, name: "开关配件", category: "ah" },
      { id: 7, name: "新品开发定制", category: "ai" }
    ]);
    const currentIndex = common_vendor.ref(0);
    const currentImages = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const getPageParams = () => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      return currentPage.options || {};
    };
    const getImagesFromCloud = async (category) => {
      try {
        loading.value = true;
        const db = common_vendor.wx$1.cloud.database();
        const _ = db.command;
        const res = await db.collection("images").where({
          category: _.eq(category)
        }).get();
        common_vendor.index.__f__("log", "at pages/design/detail.vue:84", `获取${category}分类图片:`, res.data);
        if (res.data && res.data.length > 0) {
          currentImages.value = res.data.map((item, index) => ({
            id: index + 1,
            image: item.fileid,
            filename: item.filename || `图片${index + 1}`
          }));
        } else {
          currentImages.value = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/design/detail.vue:97", `获取${category}分类图片失败:`, error);
        currentImages.value = [];
      } finally {
        loading.value = false;
      }
    };
    const selectCategory = async (idx) => {
      currentIndex.value = idx;
      const category = designCategories.value[idx].category;
      await getImagesFromCloud(category);
    };
    const previewImage = (item, index) => {
      const urls = currentImages.value.map((img) => img.image);
      common_vendor.index.previewImage({
        urls,
        current: item.image
      });
    };
    common_vendor.onMounted(() => {
      const params = getPageParams();
      let targetIndex = 0;
      if (params.id) {
        const id = parseInt(params.id);
        const foundIndex = designCategories.value.findIndex((item) => item.id === id);
        if (foundIndex !== -1) {
          targetIndex = foundIndex;
        }
      }
      if (designCategories.value.length > 0) {
        selectCategory(targetIndex);
      }
    });
    return {
      designCategories,
      currentIndex,
      currentImages,
      loading,
      selectCategory,
      previewImage
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($setup.designCategories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.n($setup.currentIndex === index ? "active" : ""),
        d: common_vendor.o(($event) => $setup.selectCategory(index), index)
      };
    }),
    b: $setup.loading
  }, $setup.loading ? {} : {
    c: common_vendor.f($setup.currentImages, (item, idx, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.filename || `图片${idx + 1}`),
        c: idx,
        d: common_vendor.o(($event) => $setup.previewImage(item, idx), idx)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/design/detail.js.map
