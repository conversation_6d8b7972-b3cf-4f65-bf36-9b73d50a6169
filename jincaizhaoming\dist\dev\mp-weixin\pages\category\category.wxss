/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.safe-area-top {
  height: var(--status-bar-height, 100px);
  width: 100%;
}
.category-root {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f8f8;
}
.category-main {
  flex: 1;
  display: flex;
  height: calc(100vh - var(--status-bar-height, 44px));
}
.category-list {
  width: 180rpx;
  background: #f4f4f4;
  height: 100%;
  overflow: auto;
}
.category-list .category-item {
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
}
.category-list .category-item.active {
  color: #3cc51f;
  font-weight: bold;
  background: #fff;
}
.goods-list {
  flex: 1;
  height: 100%;
  background: #fff;
  overflow: auto;
  padding: 0 10rpx;
}
.goods-list .goods-grid {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  padding-top: 20rpx;
  box-sizing: border-box;
}
.goods-list .goods-grid .goods-item {
  width: 47%;
  margin: 1.5%;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  padding-bottom: 20rpx;
}
.goods-list .goods-grid .goods-item .goods-img {
  width: 100%;
  height: 180rpx;
  border-radius: 12rpx 12rpx 0 0;
  background: #eee;
}
.goods-list .goods-grid .goods-item .goods-title {
  padding: 16rpx 10rpx 0 10rpx;
  font-size: 26rpx;
  color: #222;
  text-align: center;
}