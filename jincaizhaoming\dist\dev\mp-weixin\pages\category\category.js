"use strict";
const common_vendor = require("../../common/vendor.js");
const categoryMapping = {
  "客厅灯": "ba",
  "卧室灯": "bb",
  "吊灯": "bc",
  "磁吸灯": "bd",
  "线性灯": "be",
  "风扇灯": "bf",
  "餐吊灯": "bg",
  "筒灯": "bh",
  "射灯": "bi",
  "灯带": "bj",
  "过道灯": "bk",
  "壁灯": "bl",
  "浴霸": "bm",
  "集成灯": "bn",
  "T5T8灯管": "bo",
  "办公灯": "bp",
  "节日灯串": "bq",
  "光源灯配": "br",
  "开关": "bs",
  "商照": "bt"
};
const categoryNames = [
  "客厅灯",
  "卧室灯",
  "吊灯",
  "磁吸灯",
  "线性灯",
  "风扇灯",
  "餐吊灯",
  "筒灯",
  "射灯",
  "灯带",
  "过道灯",
  "壁灯",
  "浴霸",
  "集成灯",
  "T5T8灯管",
  "办公灯",
  "节日灯串",
  "光源灯配",
  "开关",
  "商照"
];
const _sfc_main = {
  setup() {
    const categories = common_vendor.ref(categoryNames);
    const currentIndex = common_vendor.ref(0);
    const currentGoods = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const getImagesFromCloud = async (categoryCode) => {
      try {
        loading.value = true;
        const db = common_vendor.wx$1.cloud.database();
        const _ = db.command;
        const res = await db.collection("images").where({
          category: _.eq(categoryCode)
        }).get();
        common_vendor.index.__f__("log", "at pages/category/category.vue:89", `获取${categoryCode}分类图片:`, res.data);
        if (res.data && res.data.length > 0) {
          currentGoods.value = res.data.map((item, index) => ({
            id: index + 1,
            image: item.fileid,
            filename: item.filename || ""
          }));
        } else {
          currentGoods.value = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:102", `获取${categoryCode}分类图片失败:`, error);
        currentGoods.value = [];
      } finally {
        loading.value = false;
      }
    };
    const selectCategory = async (idx) => {
      currentIndex.value = idx;
      const categoryName = categories.value[idx];
      const categoryCode = categoryMapping[categoryName];
      if (categoryCode) {
        await getImagesFromCloud(categoryCode);
      } else {
        common_vendor.index.__f__("warn", "at pages/category/category.vue:118", `未找到分类 ${categoryName} 对应的代码`);
        currentGoods.value = [];
      }
    };
    const previewImage = (item) => {
      const urls = currentGoods.value.map((img) => img.image);
      common_vendor.index.previewImage({
        urls,
        current: item.image
      });
    };
    common_vendor.onMounted(() => {
      if (categories.value.length > 0) {
        selectCategory(0);
      }
    });
    return {
      categories,
      currentIndex,
      currentGoods,
      loading,
      selectCategory,
      previewImage
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($setup.categories, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: common_vendor.n($setup.currentIndex === index ? "active" : ""),
        d: common_vendor.o(($event) => $setup.selectCategory(index), index)
      };
    }),
    b: $setup.loading
  }, $setup.loading ? {} : $setup.currentGoods.length > 0 ? {
    d: common_vendor.f($setup.currentGoods, (good, idx, i0) => {
      return {
        a: good.image,
        b: common_vendor.t(good.filename || `图片${idx + 1}`),
        c: idx,
        d: common_vendor.o(($event) => $setup.previewImage(good), idx)
      };
    })
  } : {}, {
    c: $setup.currentGoods.length > 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/category/category.js.map
