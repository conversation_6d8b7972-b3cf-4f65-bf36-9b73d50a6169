"use strict";
const common_vendor = require("../../common/vendor.js");
const categoryNames = [
  "客厅灯",
  "卧室灯",
  "吊灯",
  "磁吸灯",
  "线性灯",
  "风扇灯",
  "餐吊灯",
  "筒灯",
  "射灯",
  "灯带",
  "过道灯",
  "壁灯",
  "浴霸",
  "集成灯",
  "T5T8灯管",
  "办公灯",
  "节日灯串",
  "光源灯配",
  "开关",
  "商照"
];
const placeholderGoods = Array(8).fill(0).map((_, i) => ({
  image: "/static/placeholder.png",
  title: `商品${i + 1}`
}));
const _sfc_main = {
  setup() {
    const categories = common_vendor.ref(categoryNames);
    const currentIndex = common_vendor.ref(0);
    const goodsList = common_vendor.ref(Array(categories.value.length).fill(placeholderGoods));
    const selectCategory = (idx) => {
      currentIndex.value = idx;
    };
    return {
      categories,
      currentIndex,
      goodsList,
      selectCategory
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($setup.categories, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: common_vendor.n($setup.currentIndex === index ? "active" : ""),
        d: common_vendor.o(($event) => $setup.selectCategory(index), index)
      };
    }),
    b: common_vendor.f($setup.goodsList[$setup.currentIndex], (good, idx, i0) => {
      return {
        a: good.image,
        b: common_vendor.t(good.title),
        c: idx
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/category/category.js.map
