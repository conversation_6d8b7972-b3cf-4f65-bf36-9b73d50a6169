<view class="design-root"><view class="safe-area-top"></view><view class="design-main"><scroll-view class="category-list" scroll-y><view wx:for="{{a}}" wx:for-item="item" wx:key="b" class="{{['category-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></scroll-view><scroll-view class="goods-list" scroll-y><view wx:if="{{b}}" class="loading-container"><text class="loading-text">正在加载图片...</text></view><view wx:else class="goods-grid"><view wx:for="{{c}}" wx:for-item="item" wx:key="c" class="goods-item" bindtap="{{item.d}}"><image class="goods-img" src="{{item.a}}" mode="aspectFill"/><view class="goods-title">{{item.b}}</view></view></view></scroll-view></view></view>