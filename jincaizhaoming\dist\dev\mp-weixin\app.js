"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/category/category.js";
  "./pages/sunshine/sunshine.js";
  "./pages/design/detail.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.wx$1.cloud.init({
      env: "cloud1-8gakkk4ucebe9f24",
      // 在微信云控制台查看
      traceUser: true
      // 追踪用户访问
    });
    common_vendor.index.__f__("log", "at App.vue:10", "App Launch");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:13", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:16", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
